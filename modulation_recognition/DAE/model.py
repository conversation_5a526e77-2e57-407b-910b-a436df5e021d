import os

from keras.api.models import Model
from keras.api.layers import Input,Dense,Conv1D,MaxPool1D,ReLU,Dropout,Softmax,BatchNormalization,TimeDistributed
from keras.api.layers import LSTM,Bidirectional,Flatten,Reshape,Lambda
from keras.api.utils import plot_model

def get_model(weights=None,
             input_shape=[128,2],
             classes=10,
             use_cudnn=True,
             **kwargs):
    if weights is not None and not (os.path.exists(weights)):
        raise ValueError('The `weights` argument should be either '
                         '`None` (random initialization), '
                         'or the path to the weights file to be loaded.')

    input = Input(input_shape, name='input')
    x = input
    # 降低 dropout 率，避免过度正则化
    dr_lstm = 0.0  # LSTM dropout 率
    dr_dense = 0.05  # Dense layer dropout 率

    #LSTM encode Unit
    x,s,c = LSTM(units=32, dropout=dr_lstm, return_state=True, use_cudnn=use_cudnn,return_sequences=True)(x)
    x,s1,c1 = LSTM(units=32, dropout=dr_lstm, return_state=True, use_cudnn=use_cudnn, return_sequences=True)(x)

    #Classifier
    xc = Dense(32, activation='relu')(s1)
    xc = BatchNormalization()(xc)
    xc = Dropout(dr_dense)(xc)
    xc = Dense(16, activation='relu')(xc)
    xc = BatchNormalization()(xc)
    xc = Dropout(dr_dense)(xc)
    xc = Dense(classes, activation='softmax', name='xc')(xc)

    #Decoder
    xd = TimeDistributed(Dense(2),name='xd')(x)

    model = Model(inputs=input, outputs=[xc,xd])

    # Load weights.
    if weights is not None:
        model.load_weights(weights)

    return model


def train(X_train, Y_train, X_val, Y_val, sig_len, classes, weight_file, nb_epoch, batch_size, callbacks):
    model = get_model(weights=None, input_shape=[sig_len, 2], classes=len(classes))
    model.compile(optimizer='adam', jit_compile=False,
                  loss={'xc': 'categorical_crossentropy',
                        'xd': 'mean_squared_error'},
                  loss_weights={'xc': 0.15,
                                'xd': 0.85},
                  metrics={'xc': 'accuracy', 'xd': 'mse'})
    model.summary()

    history = model.fit(X_train,
        [Y_train, X_train],
        batch_size=batch_size,
        epochs=nb_epoch,
        verbose=2,
        validation_data=(X_val, [Y_val, X_val]),
        callbacks=callbacks,
    )

    history.history['accuracy'] = history.history['xc_accuracy']
    history.history['val_accuracy'] = history.history['val_xc_accuracy']
    history.history['loss'] = history.history['xc_loss']
    history.history['val_loss'] = history.history['val_xc_loss']

    return history


def predict(model, X_test, batch_size):
    [test_Y_hat, test_X_hat] = model.predict(X_test, batch_size=batch_size)
    return test_Y_hat

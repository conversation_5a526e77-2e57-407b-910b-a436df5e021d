import os
import tensorflow as tf

import numpy as np

from keras.api.optimizers import <PERSON>
from keras.api.losses import CategoricalCrossentropy

from keras.api.models import Model
from keras.api.layers import Input, Dense, Conv2D, MaxPooling2D, \
    concatenate, Flatten, Reshape, Activation, GRU, Dropout, MultiHeadAttention
from keras.api.layers import Lambda, Multiply, Add, Subtract, LayerNormalization
from keras.api.utils import plot_model

@tf.keras.utils.register_keras_serializable()
def cal1(x):
    y = tf.keras.backend.cos(x)
    return y

@tf.keras.utils.register_keras_serializable()
def cal2(x):
    y = tf.keras.backend.sin(x)
    return y


def get_model(weights=None,
           input_shape=[128, 2],
           input_shape2=[128],
           classes=11,
           use_cudnn=True,
           **kwargs):
    if weights is not None and not (os.path.exists(weights)):
        raise ValueError('The `weights` argument should be either '
                         '`None` (random initialization), '
                         'or the path to the weights file to be loaded.')

    sig_len = input_shape[0]

    dr = 0.2 # dropout rate (%)
    input = Input(input_shape+[1], name='input1')
    input1 = Input(input_shape2, name='input2')
    input2 = Input(input_shape2, name='input3')

    # 增强相位估计模块
    x1 = Flatten()(input)
    x1 = Dense(64, activation='relu', name='phase_fc1')(x1)
    x1 = Dropout(dr)(x1)
    x1 = Dense(32, activation='relu', name='phase_fc2')(x1)
    x1 = Dropout(dr)(x1)
    x1 = Dense(1, name='phase_output')(x1)
    x1 = Activation('linear')(x1)

    cos1 = Lambda(cal1)(x1)
    sin1 = Lambda(cal2)(x1)
    x11 = Multiply()([input1, cos1])
    x12 = Multiply()([input2, sin1])
    x21 = Multiply()([input2, cos1])
    x22 = Multiply()([input1, sin1])
    y1 = Add()([x11,x12])
    y2 = Subtract()([x21,x22])
    y1 = Reshape(target_shape=(sig_len, 1), name='reshape1')(y1)
    y2 = Reshape(target_shape=(sig_len, 1), name='reshape2')(y2)
    x11 = concatenate([y1, y2])
    x3 = Reshape(target_shape=((sig_len, 2, 1)), name='reshape3')(x11)

    # spatial feature - 针对相位敏感调制优化
    x3 = Conv2D(64, (4,2), padding='valid', activation="relu", name="conv1_1", kernel_initializer='he_uniform')(x3)
    x3 = Conv2D(32, (4,1), padding='valid', activation="relu", name="conv1_2", kernel_initializer='he_uniform')(x3)
    x3 = MaxPooling2D(pool_size=(2,1), strides=(2,1), name="pool1")(x3)
    x3 = Conv2D(16, (3,1), padding='valid', activation="relu", name="conv1_3", kernel_initializer='he_uniform')(x3)

    # 相应调整reshape - 新的卷积层配置
    conv_output_len = ((sig_len-6)//2-2)
    x4 = Reshape(target_shape=(conv_output_len, 16), name='reshape4')(x3)
    # temporal feature - 增强GRU + 注意力机制
    x4 = GRU(units=256, dropout=0.3, recurrent_dropout=0.2, return_sequences=True)(x4)

    # 添加自注意力机制
    attention_output = MultiHeadAttention(num_heads=8, key_dim=32)(x4, x4)
    x4 = Add()([x4, attention_output])  # 残差连接
    x4 = LayerNormalization()(x4)

    x4 = GRU(units=128, dropout=0.2)(x4)

    # 最终分类层
    xc = Dense(64, activation='relu')(x4)
    xc = Dropout(0.3)(xc)
    xc = Dense(classes, activation='softmax', name='xc')(xc)

    model = Model(inputs = [input, input1, input2], outputs=xc)

    # Load weights.
    if weights is not None:
        model.load_weights(weights)

    return model




def train(X_train, Y_train, X_val, Y_val, sig_len, classes, weight_file, nb_epoch, batch_size, callbacks):

    X1_train=X_train[:,:,0]
    X1_val=X_val[:,:,0]
    X2_train=X_train[:,:,1]
    X2_val=X_val[:,:,1]
    X_train=np.expand_dims(X_train,axis=3)
    X_val=np.expand_dims(X_val,axis=3)

    model = get_model(
        weights=None, input_shape=[sig_len, 2], 
        input_shape2=[sig_len], 
        classes=len(classes)
    )

    # 优化训练策略
    model.compile(
        loss=Adam(),
        metrics=['accuracy'],
        optimizer=CategoricalCrossentropy(label_smoothing=0.1)
    )
    model.summary()

    history = model.fit(
        [X_train,X1_train,X2_train],
        Y_train,
        batch_size=batch_size,
        epochs=nb_epoch,
        verbose=2,
        validation_data=([X_val, X1_val, X2_val], Y_val),
        callbacks=callbacks,
    )

    return history


def predict(model, X_test, batch_size):
    X1_test=X_test[:,:,0]
    X2_test=X_test[:,:,1]
    X_test=np.expand_dims(X_test,axis=3)
    return model.predict([X_test,X1_test,X2_test], batch_size=batch_size)

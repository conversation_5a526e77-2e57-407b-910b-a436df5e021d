import os
import tensorflow as tf

import numpy as np

from keras.api.models import Model
from keras.api.layers import Input, Dense, Conv2D, MaxPooling2D, \
    concatenate, Flatten, Reshape, Activation, GRU
from keras.api.layers import Lambda, Multiply, Add, Subtract
from keras.api.utils import plot_model

@tf.keras.utils.register_keras_serializable()
def cal1(x):
    y = tf.keras.backend.cos(x)
    return y

@tf.keras.utils.register_keras_serializable()
def cal2(x):
    y = tf.keras.backend.sin(x)
    return y


def get_model(weights=None,
           input_shape=[128, 2],
           input_shape2=[128],
           classes=11,
           use_cudnn=True,
           **kwargs):
    if weights is not None and not (os.path.exists(weights)):
        raise ValueError('The `weights` argument should be either '
                         '`None` (random initialization), '
                         'or the path to the weights file to be loaded.')

    sig_len = input_shape[0]

    dr = 0.1 # dropout rate (%)
    input = Input(input_shape+[1], name='input1')
    input1 = Input(input_shape2, name='input2')
    input2 = Input(input_shape2, name='input3')

    x1 = Flatten()(input)
    x1 = Dense(1, name='fc2')(x1)
    x1 = Activation('linear')(x1)

    cos1 = Lambda(cal1)(x1)
    sin1 = Lambda(cal2)(x1)
    x11 = Multiply()([input1, cos1])
    x12 = Multiply()([input2, sin1])
    x21 = Multiply()([input2, cos1])
    x22 = Multiply()([input1, sin1])
    y1 = Add()([x11,x12])
    y2 = Subtract()([x21,x22])
    y1 = Reshape(target_shape=(sig_len, 1), name='reshape1')(y1)
    y2 = Reshape(target_shape=(sig_len, 1), name='reshape2')(y2)
    x11 = concatenate([y1, y2])
    x3 = Reshape(target_shape=((sig_len, 2, 1)), name='reshape3')(x11)

    # spatial feature
    x3 = Conv2D(75, (8,2), padding='valid', activation="relu", name="conv1_1", kernel_initializer='glorot_uniform')(x3)
    x3 = Conv2D(25, (5,1), padding='valid', activation="relu", name="conv1_2", kernel_initializer='glorot_uniform')(x3)
    x3 = MaxPooling2D(pool_size=(4,1), strides=(4,1), name="pool1")(x3)  # 进一步降采样
    # temporal feature
    x4 = Reshape(target_shape=((sig_len-11,25)), name='reshape4')(x3)
    x4 = GRU(units=128)(x4)

    xc = Dense(classes, activation='softmax', name='xc')(x4)

    model = Model(inputs = [input, input1, input2], outputs=xc)

    # Load weights.
    if weights is not None:
        model.load_weights(weights)

    return model




def train(X_train, Y_train, X_val, Y_val, sig_len, classes, weight_file, nb_epoch, batch_size, callbacks):

    X1_train=X_train[:,:,0]
    X1_val=X_val[:,:,0]
    X2_train=X_train[:,:,1]
    X2_val=X_val[:,:,1]
    X_train=np.expand_dims(X_train,axis=3)
    X_val=np.expand_dims(X_val,axis=3)

    model = get_model(
        weights=None, input_shape=[sig_len, 2], 
        input_shape2=[sig_len], 
        classes=len(classes)
    )
    model.compile(
        loss='categorical_crossentropy',
        metrics=['accuracy'], 
        optimizer='adam'
    )
    model.summary()

    history = model.fit(
        [X_train,X1_train,X2_train],
        Y_train,
        batch_size=batch_size,
        epochs=nb_epoch,
        verbose=2,
        validation_data=([X_val, X1_val, X2_val], Y_val),
        callbacks=callbacks,
    )

    return history


def predict(model, X_test, batch_size):
    X1_test=X_test[:,:,0]
    X2_test=X_test[:,:,1]
    X_test=np.expand_dims(X_test,axis=3)
    return model.predict([X_test,X1_test,X2_test], batch_size=batch_size)

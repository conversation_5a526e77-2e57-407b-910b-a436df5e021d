import os, random

os.environ["KERAS_BACKEND"] = "tensorflow"
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
os.environ["TF_CPP_MIN_LOG_LEVEL"] = "2"  # Suppress TensorFlow logging (1 = INFO, 2 = WARNING, 3 = ERROR)

import csv
import numpy as np

import matplotlib.pyplot as plt 
from matplotlib.collections import LineCollection
from matplotlib.colors import ListedColormap, BoundaryNorm
import pickle, random, sys, h5py

import tensorflow as tf
import keras
from keras.api.callbacks import LearningRateScheduler, TensorBoard
from keras.api.regularizers import *
from keras.api.models import model_from_json, load_model

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import common


# keras.backend.set_floatx('float16')

import DAE as mdlDAE
import PET_CGDNN as mdlPET

import argparse

# Enable TensorFlow to use the best available CPU instructions
tf.config.optimizer.set_jit(True)  # Enable XLA (Accelerated Linear Algebra) for better performance


# 输出 CUDA 和 cuDNN 版本
cuda_version = tf.sysconfig.get_build_info()["cuda_version"]
cudnn_version = tf.sysconfig.get_build_info()["cudnn_version"]
print("CUDA version:", cuda_version)
print("cuDNN version:", cudnn_version)
print("tensorflow version:", tf.__version__)
print("GPU id:", tf.config.experimental.list_physical_devices('GPU'))
print("backend float:", keras.backend.floatx())

parser = argparse.ArgumentParser()
parser.add_argument('--mode', choices=['train', 'predict', 'both'], required=False, default='both',
                    help="Mode to run the script in: 'train', 'predict', or 'both', default is 'both'")
parser.add_argument('--model', choices=['DAE', 'PET_CGDNN'], required=False, default='DAE',
                    help="Model to use: 'DAE' or 'PET_CGDNN'")
parser.add_argument('--len', type=int, default=128, help='Length of the input signal')

args = parser.parse_args()
if args.model == 'DAE':
    print("use DAE")
    mdl = mdlDAE
elif args.model == 'PET_CGDNN':
    print("use PET_CGDNN")
    mdl = mdlPET
else:
    print("Invalid model choice. Use 'DAE' or 'PET_CGDNN'.")
    os.exit(1)


# 更改工作目录
script_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(script_dir)
weight_path = 'weights'
result_path = 'result'
weight_file = '{}/{}_{}.keras'.format(weight_path, args.model, args.len)

# 创建文件夹
if not os.path.exists(weight_path):
    os.makedirs(weight_path)
if not os.path.exists(result_path):
    os.makedirs(result_path)


sig_len = args.len
nb_epoch = 200   # number of epochs to train on (增加训练轮数)
batch_size = 400  # training batch size

# 加载模型
(mods, snrs, rates, lbl), (X_train, Y_train), (X_val, Y_val), (X_test, Y_test), (train_idx, val_idx, test_idx) = \
    common.load_data("../build-dataset/RadioSamples-rx{}.dat".format(sig_len)) # RML2016.10a_dict.pkl

# 将数据转换为 float16
X_train, X_val, X_test = mdl.prepare(X_train, X_val, X_test)

print("data size:\n")
print(f"total shape: {X_train.shape[0]+X_val.shape[0]+X_test.shape[0]}, {X_train.shape[1]}")
print(f"X_train shape: {X_train.shape}")
print(f"Y_train shape: {Y_train.shape}")
print(f"X_val shape: {X_val.shape}")
print(f"Y_val shape: {Y_val.shape}")
print(f"X_test shape: {X_test.shape}")
print(f"Y_test shape: {Y_test.shape}")

classes = mods
print(classes)
print(len(classes))

def train(weight_file):
    # 模型构建
    callbacks=[
        keras.callbacks.ModelCheckpoint(weight_file, monitor='val_loss', verbose=1, save_best_only=True, mode='auto'),
        keras.callbacks.ReduceLROnPlateau(monitor='val_loss', factor=0.5, verbose=1, patience=5, min_lr=0.000001),
        keras.callbacks.EarlyStopping(monitor='val_loss', patience=80, verbose=1, mode='auto', min_delta=0.000001)
    ]

    history = mdl.train(X_train, Y_train, X_val, Y_val, sig_len, classes, weight_file, nb_epoch, batch_size, callbacks)
    common.show_history(history, result_path)


def predict(weight_file, result_path):
    model = load_model(weight_file)
    model.summary()

    # Plot confusion matrix
    test_Y_hat = mdl.predict(model, X_test, batch_size)
    confnorm, _, _ = common.calculate_confusion_matrix(Y_test, test_Y_hat, classes)
    
    common.plot_confusion_matrix(confnorm, labels=classes, 
        save_filename='{}/Confusion(MODEL={}).png'.format(result_path, args.model))

    # Plot confusion matrix
    acc = {}
    acc_mod_snr = np.zeros((len(classes), len(snrs)))
    i = 0
    for snr in snrs:
        test_SNRs = [lbl[x][1] for x in test_idx]
        test_X_i = X_test[np.where(np.array(test_SNRs) == snr)]
        test_Y_i = Y_test[np.where(np.array(test_SNRs) == snr)]
        test_Y_i_hat = mdl.predict(model, test_X_i, batch_size)
        confnorm_i, cor, ncor = common.calculate_confusion_matrix(test_Y_i, test_Y_i_hat, classes)
        acc[snr] = 1.0 * cor / (cor + ncor)
        result = cor / (cor + ncor)
        with open('{}/acc_{}.csv'.format(result_path, args.model), 'a', newline='') as f0:
            write0 = csv.writer(f0)
            write0.writerow([result])
        common.plot_confusion_matrix(confnorm_i, labels=classes, title="Confusion Matrix", save_filename="{}/Confusion(MODEL={})(SNR={})(LEN={}).png".format(result_path, args.model, snr, test_X_i.shape[1]))

        acc_mod_snr[:, i] = np.round(np.diag(confnorm_i) / np.sum(confnorm_i, axis=1), 3)
        i = i + 1

    # Plot acc of each mod in one picture
    dis_num = 10
    for g in range(int(np.ceil(acc_mod_snr.shape[0] / dis_num))):
        assert (0 <= dis_num <= acc_mod_snr.shape[0])
        beg_index = g * dis_num
        end_index = np.min([(g + 1) * dis_num, acc_mod_snr.shape[0]])

        plt.figure(figsize=(12, 10))
        plt.xlabel("Signal to Noise Ratio")
        plt.ylabel("Classification Accuracy")
        plt.title("Classification Accuracy for Each Mod")
        for i in range(beg_index, end_index):
            plt.plot(snrs, acc_mod_snr[i], label=str(classes[i]))
            for x, y in zip(snrs, acc_mod_snr[i]):
                plt.text(x, y, str(y), ha='center', va='bottom', fontsize=8)

        plt.legend()
        plt.grid()
        plt.savefig('{}/acc_with_mod_{}.png'.format(result_path, g + 1))
        plt.close()

    # Save acc for mod per SNR
    fd = open('{}/acc_for_mod_on_{}.dat'.format(result_path, args.model), 'wb')
    pickle.dump((acc_mod_snr), fd)
    fd.close()

    # Save results to a pickle file for plotting later
    print(acc)
    fd = open('{}/acc_{}.dat'.format(result_path, args.model), 'wb')
    pickle.dump((acc), fd)

    # Plot accuracy curve
    plt.plot(snrs, list(map(lambda x: acc[x], snrs)))
    plt.xlabel("Signal to Noise Ratio")
    plt.ylabel("Classification Accuracy")
    plt.title("Classification Accuracy on Test Set")
    plt.tight_layout()
    plt.savefig('{}/each_acc_{}.png'.format(result_path, args.model))




if args.mode == 'train':
    train(weight_file)
elif args.mode == 'predict':
    predict(weight_file, result_path)
else :
    train(weight_file)
    predict(weight_file, result_path)


